const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

// Test credentials (assuming they exist from previous test)
const adminCredentials = {
  email: '<EMAIL>',
  password: 'Admin123!'
};

async function testEditExam() {
  console.log('🧪 Testing Edit Exam Functionality...\n');

  try {
    // Step 1: Login as admin
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, adminCredentials);
    const adminToken = loginResponse.data.token;
    console.log('✅ Admin login successful');

    // Step 2: Get list of exams
    console.log('2️⃣ Getting exams list...');
    const examsResponse = await axios.get(`${BASE_URL}/exams`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (examsResponse.data.data.length === 0) {
      console.log('❌ No exams found. Please run test-api.js first to create test data.');
      return;
    }

    const examToEdit = examsResponse.data.data[0];
    console.log(`✅ Found exam to edit: "${examToEdit.title}" (ID: ${examToEdit._id})`);

    // Step 3: Get exam details for editing
    console.log('3️⃣ Getting exam details...');
    const examDetailsResponse = await axios.get(`${BASE_URL}/exams/${examToEdit._id}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Exam details retrieved successfully');

    // Step 4: Update the exam
    console.log('4️⃣ Updating exam...');
    const updatedExamData = {
      title: examToEdit.title + ' (Updated)',
      description: (examToEdit.description || '') + ' - This exam has been updated for testing.',
      duration_minutes: examToEdit.duration_minutes + 5,
      questions: examToEdit.questions.map(q => ({
        ...q,
        points: q.points + 1 // Increase points by 1
      }))
    };

    const updateResponse = await axios.put(`${BASE_URL}/exams/${examToEdit._id}`, updatedExamData, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Exam updated successfully');
    console.log(`   New title: "${updateResponse.data.data.title}"`);
    console.log(`   New duration: ${updateResponse.data.data.duration_minutes} minutes`);

    // Step 5: Verify the update
    console.log('5️⃣ Verifying update...');
    const verifyResponse = await axios.get(`${BASE_URL}/exams/${examToEdit._id}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    const updatedExam = verifyResponse.data.data;
    if (updatedExam.title.includes('(Updated)')) {
      console.log('✅ Update verification successful');
    } else {
      console.log('❌ Update verification failed');
    }

    // Step 6: Test exam statistics (if there are submissions)
    console.log('6️⃣ Testing exam statistics...');
    try {
      const statsResponse = await axios.get(`${BASE_URL}/exams/${examToEdit._id}/statistics`, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      console.log('✅ Exam statistics retrieved successfully');
      console.log(`   Total submissions: ${statsResponse.data.data.statistics.total_submissions}`);
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('ℹ️ No statistics available (no submissions yet)');
      } else {
        throw error;
      }
    }

    // Step 7: Test exam results
    console.log('7️⃣ Testing exam results...');
    try {
      const resultsResponse = await axios.get(`${BASE_URL}/submissions/exams/${examToEdit._id}/results`, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      console.log('✅ Exam results retrieved successfully');
      console.log(`   Total results: ${resultsResponse.data.data.results.length}`);
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('ℹ️ No results available (no submissions yet)');
      } else {
        throw error;
      }
    }

    console.log('\n🎉 Edit exam functionality test completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('✅ Admin Authentication');
    console.log('✅ Exam Retrieval');
    console.log('✅ Exam Update');
    console.log('✅ Update Verification');
    console.log('✅ Statistics Access');
    console.log('✅ Results Access');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    console.error('Status:', error.response?.status);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Tip: Make sure the admin user exists. Run test-api.js first to create test data.');
    }
  }
}

// Run the test
testEditExam();
