<nav class="bg-white border-b border-gray-200 dark:bg-gray-900 dark:border-gray-700 mb-1 shadow-sm sticky top-0 z-50">
  <div class="max-w-screen-xl mx-auto flex flex-wrap items-center justify-between p-4">
    <a routerLink="/home"
      class="text-2xl font-bold text-blue-700 dark:text-white hover:text-blue-800 dark:hover:text-blue-400 transition-colors">
      Exam System
    </a>

    <button data-collapse-toggle="navbar-default" type="button"
      class="inline-flex items-center p-2 w-10 h-10 justify-center text-gray-600 md:hidden hover:bg-gray-100 focus:outline-none dark:text-gray-300 dark:hover:bg-gray-700 rounded-md transition"
      aria-controls="navbar-default" aria-expanded="false" aria-label="Toggle navigation menu">
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
      </svg>
    </button>

    <div class="hidden w-full md:flex md:items-center md:w-auto" id="navbar-default">
      <ul
        class="flex flex-col md:flex-row md:space-x-8 font-medium w-full md:w-auto bg-gray-50 dark:bg-gray-800 md:bg-transparent rounded-lg md:rounded-none p-4 md:p-0 border border-gray-100 md:border-0 dark:border-gray-700">
        <li>
          <a [routerLink]="['/home']" routerLinkActive="text-blue-700 font-semibold"
            [routerLinkActiveOptions]="{ exact: true }"
            class="block py-2 px-3 rounded text-gray-900 hover:text-blue-700 dark:text-white dark:hover:text-blue-400 transition-colors">Home</a>
        </li>
        <li>
          <a [routerLink]="['/about']" routerLinkActive="text-blue-700 font-semibold"
            class="block py-2 px-3 rounded text-gray-900 hover:text-blue-700 dark:text-white dark:hover:text-blue-400 transition-colors">About
            Us</a>
        </li>
        <li *ngIf="authService.isLoggedIn()">
          <a [routerLink]="['/results']" routerLinkActive="text-blue-700 font-semibold"
            class="block py-2 px-3 rounded text-gray-900 hover:text-blue-700 dark:text-white dark:hover:text-blue-400 transition-colors">Results</a>
        </li>
        <!-- Student Navigation -->
        <li *ngIf="isStudent()">
          <a [routerLink]="['/student/dashboard']" routerLinkActive="text-blue-700 font-semibold"
            class="block py-2 px-3 rounded text-gray-900 hover:text-blue-700 dark:text-white dark:hover:text-blue-400 transition-colors">Dashboard</a>
        </li>
        <li *ngIf="isStudent()">
          <a [routerLink]="['/student/exams']" routerLinkActive="text-blue-700 font-semibold"
            class="block py-2 px-3 rounded text-gray-900 hover:text-blue-700 dark:text-white dark:hover:text-blue-400 transition-colors">Browse
            Exams</a>
        </li>
        <li *ngIf="isStudent()">
          <a [routerLink]="['/student/results']" routerLinkActive="text-blue-700 font-semibold"
            class="block py-2 px-3 rounded text-gray-900 hover:text-blue-700 dark:text-white dark:hover:text-blue-400 transition-colors">My
            Results</a>
        </li>

        <!-- Admin Navigation -->
        <li *ngIf="isAdmin()">
          <a [routerLink]="['/admin/dashboard']" routerLinkActive="text-blue-700 font-semibold"
            class="block py-2 px-3 rounded text-gray-900 hover:text-blue-700 dark:text-white dark:hover:text-blue-400 transition-colors">Dashboard</a>
        </li>
        <li *ngIf="isAdmin()">
          <a [routerLink]="['/admin/exams']" routerLinkActive="text-blue-700 font-semibold"
            class="block py-2 px-3 rounded text-gray-900 hover:text-blue-700 dark:text-white dark:hover:text-blue-400 transition-colors">Manage
            Exams</a>
        </li>

        <li class="md:ml-auto flex items-center space-x-6 mt-4 md:mt-0">
          <ng-container *ngIf="!authService.isLoggedIn(); else loggedIn">
            <a routerLink="/login"
              class="text-blue-600 hover:underline hover:text-blue-800 dark:text-blue-400 transition">Login</a>
            <a routerLink="/register"
              class="text-blue-600 hover:underline hover:text-blue-800 dark:text-blue-400 transition">Register</a>
          </ng-container>

          <ng-template #loggedIn>
            <!-- User Info -->
            <div class="flex items-center space-x-3">
              <span class="text-gray-700 text-sm">Welcome, {{ user?.username }}!</span>
              <span class="px-2 py-1 text-xs font-semibold rounded-full" [class.bg-purple-100]="user?.role === 'admin'"
                [class.text-purple-800]="user?.role === 'admin'" [class.bg-green-100]="user?.role === 'student'"
                [class.text-green-800]="user?.role === 'student'">
                {{ user?.role | titlecase }}
              </span>
            </div>

            <!-- Action Buttons -->
            <button type="button" (click)="navigateToDashboard()"
              class="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors">
              <span *ngIf="user?.role === 'admin'">Dashboard</span>
              <span *ngIf="user?.role === 'student'">My Exams</span>
            </button>

            <button type="button" (click)="onLogout()"
              class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors">
              Logout
            </button>
          </ng-template>
        </li>
      </ul>
    </div>
  </div>
</nav>