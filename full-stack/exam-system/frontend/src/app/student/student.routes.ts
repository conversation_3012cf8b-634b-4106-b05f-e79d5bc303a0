import { Routes } from '@angular/router';
import { StudentDashboardComponent } from './dashboard/dashboard.component';
import { ExamsComponent } from './exams/exams.component';
import { ExamComponent } from './exam/exam.component';
import { ResultsComponent } from './results/results.component';

export const STUDENT_ROUTES: Routes = [
  { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
  { path: 'dashboard', component: StudentDashboardComponent },
  { path: 'exams', component: ExamsComponent },
  { path: 'exams/:id', component: ExamComponent },
  { path: 'results', component: ResultsComponent }
];