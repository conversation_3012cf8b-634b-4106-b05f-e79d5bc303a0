import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { ExamService } from '../../core/services/exam.service';
import { SubmissionService } from '../../core/services/submission.service';
import { AuthService } from '../../shared/auth.service';

@Component({
  selector: 'app-exams',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './exams.component.html',
  styleUrls: ['./exams.component.css']
})
export class ExamsComponent implements OnInit {
  exams: any[] = [];
  filteredExams: any[] = [];
  completedExams: Set<string> = new Set();
  loading = false;
  error = '';
  searchTerm = '';
  sortBy = 'title';
  sortOrder = 'asc';

  constructor(
    private examService: ExamService,
    private submissionService: SubmissionService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadExams();
    this.loadCompletedExams();
  }

  loadExams(): void {
    // Check authentication first
    if (!this.authService.isLoggedIn()) {
      this.router.navigate(['/login']);
      return;
    }

    this.loading = true;
    this.error = '';

    this.examService.getExams().subscribe({
      next: (response) => {
        this.exams = response.data || [];
        this.filteredExams = [...this.exams];
        this.applyFilters();
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading exams:', err);
        if (err.status === 401) {
          this.authService.logout();
          return;
        }
        this.error = 'Failed to load exams. Please try again.';
        this.loading = false;
      }
    });
  }

  loadCompletedExams(): void {
    this.submissionService.getStudentResults().subscribe({
      next: (response) => {
        this.completedExams = new Set(response.data.map((submission: any) => submission.exam_id));
      },
      error: (err) => {
        console.error('Error loading completed exams:', err);
      }
    });
  }

  applyFilters(): void {
    let filtered = [...this.exams];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(exam =>
        exam.title.toLowerCase().includes(term) ||
        (exam.description && exam.description.toLowerCase().includes(term))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[this.sortBy];
      let bValue = b[this.sortBy];

      if (this.sortBy === 'createdAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (this.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    this.filteredExams = filtered;
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onSortChange(): void {
    this.applyFilters();
  }

  isExamCompleted(examId: string): boolean {
    return this.completedExams.has(examId);
  }

  getExamStatusBadge(examId: string): string {
    return this.isExamCompleted(examId)
      ? 'bg-green-100 text-green-800'
      : 'bg-blue-100 text-blue-800';
  }

  getExamStatusText(examId: string): string {
    return this.isExamCompleted(examId) ? 'Completed' : 'Available';
  }

  refreshExams(): void {
    this.loadExams();
    this.loadCompletedExams();
  }
}