<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

    <!-- Header -->
    <div class="mb-8">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Available Exams</h1>
          <p class="mt-1 text-sm text-gray-600">Choose an exam to get started</p>
        </div>
        <div class="flex space-x-3">
          <button routerLink="/student/dashboard"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
            ← Dashboard
          </button>
          <button (click)="refreshExams()"
            class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Search and Filter Controls -->
    <div class="mb-6 bg-white shadow rounded-lg p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-2">Search Exams</label>
          <input type="text" [(ngModel)]="searchTerm" (input)="onSearchChange()"
            placeholder="Search by title or description..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
          <select [(ngModel)]="sortBy" (change)="onSortChange()"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
            <option value="title">Title</option>
            <option value="duration_minutes">Duration</option>
            <option value="createdAt">Date Created</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <div *ngIf="error" class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-red-800">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="flex justify-center items-center h-64">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">Loading exams...</p>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && !error && filteredExams.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
        </path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No exams found</h3>
      <p class="mt-1 text-sm text-gray-500">
        {{ searchTerm ? 'Try adjusting your search criteria.' : 'No exams are available at the moment.' }}
      </p>
    </div>

    <!-- Exams Grid -->
    <div *ngIf="!loading && !error && filteredExams.length > 0"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div *ngFor="let exam of filteredExams"
        class="bg-white shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
        <div class="p-6">
          <div class="flex justify-between items-start mb-4">
            <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">{{ exam.title }}</h3>
            <span [class]="getExamStatusBadge(exam._id)"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
              {{ getExamStatusText(exam._id) }}
            </span>
          </div>

          <p class="text-sm text-gray-600 mb-4 line-clamp-3" *ngIf="exam.description">
            {{ exam.description }}
          </p>

          <div class="space-y-2 mb-6">
            <div class="flex items-center text-sm text-gray-500">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              {{ exam.duration_minutes }} minutes
            </div>
            <div class="flex items-center text-sm text-gray-500">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                </path>
              </svg>
              {{ exam.questions?.length || 0 }} questions
            </div>
            <div class="flex items-center text-sm text-gray-500">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              Created {{ exam.createdAt | date:'short' }}
            </div>
          </div>

          <div class="flex justify-between items-center">
            <div class="text-xs text-gray-500">
              Created by {{ exam.created_by?.username || 'Admin' }}
            </div>
            <a [routerLink]="['/student/exams', exam._id]"
              [class]="isExamCompleted(exam._id) ? 'bg-green-600 hover:bg-green-700' : 'bg-indigo-600 hover:bg-indigo-700'"
              class="text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
              {{ isExamCompleted(exam._id) ? 'Retake Exam' : 'Start Exam' }}
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Results Summary -->
    <div *ngIf="!loading && filteredExams.length > 0" class="mt-8 text-center text-sm text-gray-500">
      Showing {{ filteredExams.length }} of {{ exams.length }} exams
      <span *ngIf="searchTerm"> matching "{{ searchTerm }}"</span>
    </div>
  </div>
</div>