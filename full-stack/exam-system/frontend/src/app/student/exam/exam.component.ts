import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { ExamService } from '../../core/services/exam.service';
import { SubmissionService } from '../../core/services/submission.service';
import { AuthService } from '../../shared/auth.service';

@Component({
  selector: 'app-exam',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './exam.component.html'
})
export class ExamComponent implements OnInit, OnDestroy {
  examId: string = '';
  exam: any = null;
  loading = true;
  submitting = false;
  error = '';
  answers: any[] = [];
  timeLeft: number = 0;
  timerInterval: any;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private examService: ExamService,
    private submissionService: SubmissionService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.examId = this.route.snapshot.paramMap.get('id') || '';
    if (this.examId) {
      this.loadExam();
    } else {
      this.error = 'Invalid exam ID';
      this.loading = false;
    }
  }

  loadExam(): void {
    this.examService.getExam(this.examId).subscribe({
      next: (response) => {
        this.exam = response.data;
        this.timeLeft = this.exam.duration_minutes * 60;
        this.startTimer();
        this.initializeAnswers();
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading exam:', err);
        this.error = 'Failed to load exam. Please try again.';
        this.loading = false;
      }
    });
  }

  initializeAnswers(): void {
    this.answers = this.exam.questions.map((q: any) => ({
      question_id: q._id,
      selected_option_id: null,
      text_answer: ''
    }));
  }

  startTimer(): void {
    this.timerInterval = setInterval(() => {
      if (this.timeLeft > 0) {
        this.timeLeft--;
      } else {
        this.submitExam();
      }
    }, 1000);
  }

  formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  }

  submitExam(): void {
    if (this.submitting) return;

    clearInterval(this.timerInterval);
    this.submitting = true;
    this.error = '';

    this.submissionService.submitExam(this.examId, this.answers).subscribe({
      next: (response) => {
        const score = response.data?.score || 0;
        const total = response.data?.total_points || 0;
        const percentage = response.data?.percentage || 0;

        this.router.navigate(['/student/results'], {
          state: {
            examTitle: this.exam?.title || 'Exam',
            score,
            total,
            percentage,
            justSubmitted: true
          }
        });
      },
      error: (err) => {
        console.error('Error submitting exam:', err);
        this.error = err.error?.message || 'Failed to submit exam. Please try again.';
        this.submitting = false;
        // Restart timer if submission failed
        this.startTimer();
      }
    });
  }

  ngOnDestroy(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }
}