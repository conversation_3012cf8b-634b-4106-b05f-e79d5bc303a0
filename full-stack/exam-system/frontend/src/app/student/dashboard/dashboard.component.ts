import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink, Router } from '@angular/router';
import { ExamService } from '../../core/services/exam.service';
import { SubmissionService } from '../../core/services/submission.service';
import { AuthService } from '../../shared/auth.service';

@Component({
  selector: 'app-student-dashboard',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class StudentDashboardComponent implements OnInit {
  availableExams: any[] = [];
  recentResults: any[] = [];
  totalExamsCompleted = 0;
  averageScore = 0;
  loading = false;
  error = '';
  currentUser: any = null;

  constructor(
    private examService: ExamService,
    private submissionService: SubmissionService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    // Check authentication first
    if (!this.authService.isLoggedIn()) {
      this.router.navigate(['/login']);
      return;
    }

    this.loading = true;
    this.error = '';

    // Load available exams
    this.examService.getExams().subscribe({
      next: (response) => {
        this.availableExams = response.data.slice(0, 5); // Show latest 5 exams
      },
      error: (error) => {
        console.error('Error loading exams:', error);
        if (error.status === 401) {
          this.authService.logout();
          return;
        }
        this.error = 'Failed to load available exams';
      }
    });

    // Load student results
    this.submissionService.getStudentResults().subscribe({
      next: (response) => {
        this.recentResults = response.data.slice(0, 5); // Show latest 5 results
        this.totalExamsCompleted = response.data.length;

        // Calculate average score
        if (response.data.length > 0) {
          const totalScore = response.data.reduce((sum: number, result: any) => sum + result.score, 0);
          this.averageScore = Math.round(totalScore / response.data.length);
        }

        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading results:', error);
        if (error.status === 401) {
          this.authService.logout();
          return;
        }
        this.loading = false;
      }
    });
  }

  refreshDashboard(): void {
    this.loadDashboardData();
  }

  getScoreColor(percentage: number): string {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  }

  getScoreBadgeColor(percentage: number): string {
    if (percentage >= 80) return 'bg-green-100 text-green-800';
    if (percentage >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  }
}
