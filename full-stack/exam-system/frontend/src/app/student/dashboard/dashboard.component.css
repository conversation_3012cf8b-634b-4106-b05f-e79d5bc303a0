/* Custom styles for student dashboard */
.dashboard-card {
  transition: transform 0.2s ease-in-out;
}

.dashboard-card:hover {
  transform: translateY(-2px);
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.exam-card {
  border-left: 4px solid #4f46e5;
}

.result-card {
  border-left: 4px solid #10b981;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.score-excellent {
  background-color: #d1fae5;
  color: #065f46;
}

.score-good {
  background-color: #fef3c7;
  color: #92400e;
}

.score-needs-improvement {
  background-color: #fee2e2;
  color: #991b1b;
}
