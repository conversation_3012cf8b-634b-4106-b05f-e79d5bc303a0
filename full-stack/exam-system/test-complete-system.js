#!/usr/bin/env node

const axios = require('axios');
const colors = require('colors');

const BACKEND_URL = 'http://localhost:3001';
const FRONTEND_URL = 'http://localhost:46723'; // Update this with actual frontend port

console.log('🧪 Testing Complete Exam System Integration\n'.cyan.bold);

async function testSystemHealth() {
  const tests = [];
  
  try {
    // Test 1: Backend Health
    console.log('1️⃣ Testing Backend Health...'.yellow);
    try {
      const backendResponse = await axios.get(`${BACKEND_URL}/api/test`);
      console.log('✅ Backend is running and responding'.green);
      console.log(`   Response: ${backendResponse.data.message}`.gray);
      tests.push({ name: 'Backend Health', status: 'PASS' });
    } catch (error) {
      console.log('❌ Backend is not responding'.red);
      console.log(`   Error: ${error.message}`.gray);
      tests.push({ name: 'Backend Health', status: 'FAIL' });
    }

    // Test 2: Frontend Health
    console.log('\n2️⃣ Testing Frontend Health...'.yellow);
    try {
      const frontendResponse = await axios.get(FRONTEND_URL, { timeout: 5000 });
      if (frontendResponse.status === 200) {
        console.log('✅ Frontend is running and accessible'.green);
        tests.push({ name: 'Frontend Health', status: 'PASS' });
      }
    } catch (error) {
      console.log('❌ Frontend is not accessible'.red);
      console.log(`   Error: ${error.message}`.gray);
      console.log(`   Make sure Angular dev server is running on ${FRONTEND_URL}`.gray);
      tests.push({ name: 'Frontend Health', status: 'FAIL' });
    }

    // Test 3: Database Connection
    console.log('\n3️⃣ Testing Database Connection...'.yellow);
    try {
      const dbResponse = await axios.get(`${BACKEND_URL}/api/exams`);
      console.log('✅ Database connection is working'.green);
      console.log(`   Found ${dbResponse.data.data.length} exams in database`.gray);
      tests.push({ name: 'Database Connection', status: 'PASS' });
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Database connection is working (authentication required)'.green);
        tests.push({ name: 'Database Connection', status: 'PASS' });
      } else {
        console.log('❌ Database connection failed'.red);
        console.log(`   Error: ${error.message}`.gray);
        tests.push({ name: 'Database Connection', status: 'FAIL' });
      }
    }

    // Test 4: Authentication System
    console.log('\n4️⃣ Testing Authentication System...'.yellow);
    try {
      const loginResponse = await axios.post(`${BACKEND_URL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'Admin123!'
      });
      
      if (loginResponse.data.token) {
        console.log('✅ Authentication system is working'.green);
        console.log(`   Admin login successful`.gray);
        tests.push({ name: 'Authentication System', status: 'PASS' });
        
        // Test 5: Protected Route Access
        console.log('\n5️⃣ Testing Protected Route Access...'.yellow);
        try {
          const protectedResponse = await axios.get(`${BACKEND_URL}/api/exams`, {
            headers: { Authorization: `Bearer ${loginResponse.data.token}` }
          });
          console.log('✅ Protected routes are working'.green);
          console.log(`   Accessed exams with authentication`.gray);
          tests.push({ name: 'Protected Route Access', status: 'PASS' });
        } catch (error) {
          console.log('❌ Protected routes are not working'.red);
          tests.push({ name: 'Protected Route Access', status: 'FAIL' });
        }
      }
    } catch (error) {
      console.log('❌ Authentication system failed'.red);
      console.log(`   Error: ${error.response?.data?.message || error.message}`.gray);
      tests.push({ name: 'Authentication System', status: 'FAIL' });
      tests.push({ name: 'Protected Route Access', status: 'SKIP' });
    }

    // Test 6: CORS Configuration
    console.log('\n6️⃣ Testing CORS Configuration...'.yellow);
    try {
      const corsResponse = await axios.get(`${BACKEND_URL}/api/test`, {
        headers: { 'Origin': FRONTEND_URL }
      });
      console.log('✅ CORS is properly configured'.green);
      tests.push({ name: 'CORS Configuration', status: 'PASS' });
    } catch (error) {
      console.log('❌ CORS configuration issue'.red);
      tests.push({ name: 'CORS Configuration', status: 'FAIL' });
    }

    // Test Summary
    console.log('\n📊 Test Summary'.cyan.bold);
    console.log('='.repeat(50).gray);
    
    const passed = tests.filter(t => t.status === 'PASS').length;
    const failed = tests.filter(t => t.status === 'FAIL').length;
    const skipped = tests.filter(t => t.status === 'SKIP').length;
    
    tests.forEach(test => {
      const status = test.status === 'PASS' ? '✅ PASS'.green : 
                    test.status === 'FAIL' ? '❌ FAIL'.red : 
                    '⏭️ SKIP'.yellow;
      console.log(`${test.name.padEnd(25)} ${status}`);
    });
    
    console.log('='.repeat(50).gray);
    console.log(`Total Tests: ${tests.length}`.white);
    console.log(`Passed: ${passed}`.green);
    console.log(`Failed: ${failed}`.red);
    console.log(`Skipped: ${skipped}`.yellow);
    
    if (failed === 0) {
      console.log('\n🎉 All tests passed! The exam system is fully functional.'.green.bold);
      console.log('\n🚀 You can now:'.cyan);
      console.log(`   • Access the frontend at: ${FRONTEND_URL}`.white);
      console.log(`   • Use admin account: <EMAIL> / Admin123!`.white);
      console.log(`   • Use student account: <EMAIL> / Student123!`.white);
      console.log(`   • API documentation available at: ${BACKEND_URL}/api/test`.white);
    } else {
      console.log('\n⚠️ Some tests failed. Please check the issues above.'.yellow.bold);
    }

  } catch (error) {
    console.error('❌ System test failed:'.red, error.message);
  }
}

// Run the tests
testSystemHealth().catch(console.error);
