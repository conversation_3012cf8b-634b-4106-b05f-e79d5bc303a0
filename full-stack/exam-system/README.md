# 🎓 Complete Exam System - Full Stack Application

A comprehensive exam management system built with **Angular** (frontend) and **Node.js/Express** (backend), featuring role-based authentication, real-time exam taking, and detailed analytics.

## 🌟 Features

### 👨‍💼 Admin Features
- **Dashboard**: Overview of exams, students, and submissions
- **Exam Management**: Create, edit, delete, and manage exams
- **Question Builder**: Support for multiple-choice and text questions
- **Real-time Statistics**: View exam performance and analytics
- **Student Results**: Monitor all student submissions and scores
- **Role-based Access Control**: Secure admin-only operations

### 👨‍🎓 Student Features
- **Student Dashboard**: Personal overview with available exams and recent results
- **Exam Browser**: Search and filter available exams
- **Interactive Exam Taking**: Timed exams with auto-submit
- **Results Tracking**: View personal exam history and scores
- **Progress Monitoring**: Track completion status and performance

### 🔐 Authentication & Security
- JWT-based authentication
- Role-based authorization (Admin/Student)
- Secure password hashing with bcrypt
- Protected API endpoints
- Session management

## 🛠️ Technology Stack

### Frontend (Angular 19)
- **Framework**: Angular 19 with standalone components
- **Styling**: TailwindCSS for modern UI
- **HTTP Client**: Angular HttpClient with interceptors
- **Routing**: Angular Router with lazy loading
- **State Management**: RxJS observables and services

### Backend (Node.js/Express)
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Express-validator
- **Security**: Helmet, CORS, bcrypt

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (local or cloud instance)
- Angular CLI (`npm install -g @angular/cli`)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd full-stack/exam-system
   ```

2. **Backend Setup**
   ```bash
   cd backend
   npm install
   
   # Create .env file
   echo "MONGODB_URI=mongodb://localhost:27017/exam-system" > .env
   echo "JWT_SECRET=your_super_secret_jwt_key_here" >> .env
   echo "PORT=3001" >> .env
   
   # Start the backend server
   npm start
   ```

3. **Frontend Setup**
   ```bash
   cd ../frontend
   npm install
   
   # Start the frontend development server
   npm start
   ```

4. **Access the Application**
   - Frontend: http://localhost:4200 (or the port shown in terminal)
   - Backend API: http://localhost:3001

## 📊 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login

### Exams (Admin)
- `GET /api/exams` - Get all exams
- `POST /api/exams` - Create new exam
- `PUT /api/exams/:id` - Update exam
- `DELETE /api/exams/:id` - Delete exam
- `GET /api/exams/:id/statistics` - Get exam statistics

### Submissions
- `POST /api/submissions/exams/:id/submit` - Submit exam (Student)
- `GET /api/submissions/results` - Get student results
- `GET /api/submissions/exams/:id/results` - Get exam results (Admin)

## 🧪 Testing

### Backend API Testing
```bash
cd backend
node test-api.js    # Full API test suite
node test-edit.js   # Test edit functionality
```

### Test Accounts
The system includes pre-created test accounts:

**Admin Account:**
- Email: <EMAIL>
- Password: Admin123!

**Student Account:**
- Email: <EMAIL>
- Password: Student123!

## 🏗️ Project Structure

```
exam-system/
├── backend/
│   ├── controllers/     # Request handlers
│   ├── middleware/      # Authentication & validation
│   ├── models/         # MongoDB schemas
│   ├── repositories/   # Data access layer
│   ├── routes/         # API routes
│   ├── services/       # Business logic
│   ├── utils/          # Utilities & validators
│   └── app.js          # Express application
├── frontend/
│   ├── src/
│   │   ├── app/
│   │   │   ├── admin/      # Admin components
│   │   │   ├── student/    # Student components
│   │   │   ├── auth/       # Authentication
│   │   │   ├── core/       # Services & models
│   │   │   └── shared/     # Shared components
│   │   └── environments/   # Environment configs
└── README.md
```

## 🔧 Configuration

### Environment Variables (Backend)
```env
MONGODB_URI=mongodb://localhost:27017/exam-system
JWT_SECRET=your_super_secret_jwt_key_here
PORT=3001
NODE_ENV=development
```

### Environment Configuration (Frontend)
```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:3001/api'
};
```

## 🎯 Key Features Implemented

### ✅ Fixed Issues
- **Edit Exam Functionality**: Fixed permission issues and duplicate functions
- **CRUD Operations**: Complete Create, Read, Update, Delete for exams
- **Backend-Frontend Connection**: Proper API integration with error handling
- **Authentication Flow**: JWT-based auth with role-based access
- **Form Validation**: Comprehensive client and server-side validation

### ✅ Enhanced Components
- **Admin Dashboard**: Statistics, quick actions, and navigation
- **Student Dashboard**: Personal overview with exam status tracking
- **Exam Management**: Advanced form with question builder
- **Navigation**: Role-based navigation with proper routing
- **Error Handling**: User-friendly error messages and loading states

## 🚀 Deployment

### Backend Deployment
1. Set production environment variables
2. Use PM2 or similar process manager
3. Configure reverse proxy (nginx)
4. Set up MongoDB connection

### Frontend Deployment
1. Build for production: `ng build --prod`
2. Serve static files from `dist/` folder
3. Configure environment for production API URL

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the API test files for usage examples
- Review the component documentation
- Test with the provided test accounts

---

**🎉 The exam system is now fully functional with complete frontend-backend integration!**
